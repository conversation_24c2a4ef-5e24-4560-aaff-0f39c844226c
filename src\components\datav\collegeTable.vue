<template>
  <div id="college-table">
    <div class="table-header">
      <h3>二级学院数据统计</h3>
    </div>

    <!-- 自动循环滚动表格 -->
    <div class="table-content">
      <dv-scroll-board :config="config" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollegeTable',
  data () {
    return {
      // 学院数据
      collegeData: [
        ['石油化工学院', '99', '18', '81', '5', '1'],
        ['传媒学院', '94', '13', '81', '8', '2'],
        ['信息工程学院', '100', '22', '78', '6', '3'],
        ['交通工程学院', '92', '17', '75', '7', '4'],
        ['资源环境学院', '86', '15', '71', '9', '5'],
        ['电气工程学院', '88', '22', '66', '4', '6'],
        ['生物工程学院', '85', '25', '60', '10', '7'],
        ['经济管理学院', '78', '12', '66', '12', '8'],
        ['纺织服装学院', '75', '15', '60', '8', '9'],
        ['新媒体产业学院', '70', '10', '60', '15', '10']
      ]
    }
  },
  computed: {
    config () {
      return {
        header: ['二级学院', '出门', '进门', '校外', '未按时', '排名'],
        data: this.collegeData,
        index: false, // 不显示序号，因为我们有排名列
        columnWidth: [160, 70, 70, 70, 100, 70], // 调整列宽，确保内容完整显示
        align: ['left', 'center', 'center', 'center', 'center', 'center'], // 对齐方式
        rowNum: 5, // 显示行数
        headerBGC: 'rgb(33 50 95)', // 表头背景色
        headerHeight: 32, // 减小表头高度
        rowHeight: 28, // 设置行高
        oddRowBGC: 'rgba(0, 44, 81, 0.8)', // 奇数行背景色
        evenRowBGC: 'rgba(10, 29, 50, 0.8)', // 偶数行背景色
        waitTime: 3000, // 滚动等待时间（毫秒）
        carousel: 'single', // 滚动方式：single（单行）或 page（整页）
        hoverPause: true // 鼠标悬停时暂停滚动
      }
    }
  },
  mounted () {
    // 组件挂载后开始数据更新循环
    this.startDataUpdate()
  },
  beforeDestroy () {
    // 组件销毁前清除定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  methods: {
    // 开始数据更新循环
    startDataUpdate () {
      this.updateTimer = setInterval(() => {
        this.updateCollegeData()
      }, 10000) // 每10秒更新一次数据
    },

    // 更新学院数据（模拟实时数据变化）
    updateCollegeData () {
      this.collegeData = this.collegeData.map(college => {
        const [name, outCount, inCount, , notReturnedCount] = college

        // 随机变化数据（模拟实时更新）
        const newOutCount = Math.max(0, parseInt(outCount) + Math.floor(Math.random() * 10 - 5))
        const newInCount = Math.max(0, parseInt(inCount) + Math.floor(Math.random() * 6 - 3))
        const newOutsideCount = Math.max(0, newOutCount - newInCount)
        const newNotReturnedCount = Math.max(0, parseInt(notReturnedCount) + Math.floor(Math.random() * 6 - 3))

        return [
          name,
          newOutCount.toString(),
          newInCount.toString(),
          newOutsideCount.toString(),
          newNotReturnedCount.toString(),
          '0' // 临时排名，稍后更新
        ]
      })

      // 根据校外人数重新排序
      this.collegeData.sort((a, b) => parseInt(b[3]) - parseInt(a[3]))

      // 更新排名
      this.collegeData = this.collegeData.map((college, index) => {
        return [
          college[0], // 学院名称
          college[1], // 出门人数
          college[2], // 进门人数
          college[3], // 校外人数
          college[4], // 未按时回校人数
          (index + 1).toString() // 新排名
        ]
      })
    }
  }
}
</script>

<style lang="less">
#college-table {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(1, 153, 209, 0.3); // 边框
  border-radius: 15px;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .table-header {
    margin-bottom: 15px;
    text-align: center;

    h3 {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      text-shadow: 0 0 10px rgba(25, 129, 246, 0.8);
      letter-spacing: 2px;
    }
  }

  .table-content {
    flex: 1;
    overflow: hidden;

    // 自定义滚动表格样式
    /deep/ .dv-scroll-board {
      .header {
        background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
        box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

        .header-item {
          color: #ffffff;
          font-weight: 600;
          font-size: 12px; // 减小表头字体
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }

      .rows {
        .row-item {
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:hover {
            background-color: rgba(25, 129, 246, 0.2) !important;
            transform: translateX(5px);
          }

          .ceil {
            color: #ffffff;
            font-size: 11px; // 减小内容字体

            // 学院名称列特殊样式
            &:first-child {
              color: #64b5f6;
              font-weight: 500;
              font-size: 10px; // 学院名称字体更小
            }

            // 数字列样式
            &:nth-child(2),
            &:nth-child(3),
            &:nth-child(4),
            &:nth-child(5) {
              color: #81c784;
              font-weight: 500;
            }

            // 排名列样式
            &:nth-child(6) {
              color: #ffb74d;
              font-weight: 600;
              font-size: 12px; // 稍微减小排名字体
            }
          }
        }

        // 排名前三的特殊样式
        .row-item:nth-child(1) .ceil:nth-child(6) {
          color: #ffd700; // 金色
          text-shadow: 0 0 5px rgba(255, 215, 0, 0.8);
        }

        .row-item:nth-child(2) .ceil:nth-child(6) {
          color: #c0c0c0; // 银色
          text-shadow: 0 0 5px rgba(192, 192, 192, 0.8);
        }

        .row-item:nth-child(3) .ceil:nth-child(6) {
          color: #cd7f32; // 铜色
          text-shadow: 0 0 5px rgba(205, 127, 50, 0.8);
        }
      }
    }
  }
}
</style>
