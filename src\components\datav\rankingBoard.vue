<template>
  <div id="ranking-board">
    <div class="ranking-board-title">学院第三方人员情况</div>
    <div class="table-container">
      <!-- 自动滚动表格 -->
      <dv-scroll-board :config="config" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'RankingBoard',
  data () {
    return {
      personnelData: [
        ['学工部', '8人', '<span style="color: #52c41a;">● 正常</span>'],
        ['后勤处', '15人', '<span style="color: #52c41a;">● 正常</span>'],
        ['保卫处', '12人', '<span style="color: #52c41a;">● 正常</span>'],
        ['信息中心', '6人', '<span style="color: #ff7875;">● 异常</span>'],
        ['教务处', '10人', '<span style="color: #52c41a;">● 正常</span>'],
        ['财务处', '7人', '<span style="color: #52c41a;">● 正常</span>'],
        ['人事处', '9人', '<span style="color: #52c41a;">● 正常</span>'],
        ['科研处', '5人', '<span style="color: #ff7875;">● 异常</span>']
      ]
    }
  },
  computed: {
    config () {
      return {
        header: ['部门', '人数', '状态'],
        data: this.personnelData,
        index: true, // 显示序号
        columnWidth: [120, 80, 100], // 列宽设置
        align: ['left', 'center', 'center'], // 对齐方式
        rowNum: 4, // 显示行数
        headerBGC: 'rgb(33 50 95)', // 表头背景色
        headerHeight: 40, // 表头高度
        rowHeight: 35, // 行高
        oddRowBGC: 'rgba(0, 44, 81, 0.8)', // 奇数行背景色
        evenRowBGC: 'rgba(10, 29, 50, 0.8)', // 偶数行背景色
        waitTime: 3000, // 滚动等待时间（毫秒）
        carousel: 'single', // 滚动方式：single（单行）或 page（整页）
        hoverPause: true // 鼠标悬停时暂停滚动
      }
    }
  },
  mounted () {
    // 组件挂载后开始数据更新循环
    this.startDataUpdate()
  },
  beforeDestroy () {
    // 组件销毁前清除定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  methods: {
    // 模拟数据更新
    startDataUpdate () {
      this.updateTimer = setInterval(() => {
        // 这里可以添加实际的数据更新逻辑
        // 例如从API获取最新数据
        this.updatePersonnelData()
      }, 30000) // 30秒更新一次数据
    },
    // 更新人员数据
    updatePersonnelData () {
      // 模拟数据变化
      const departments = ['学工部', '后勤处', '保卫处', '信息中心', '教务处', '财务处', '人事处', '科研处']

      this.personnelData = departments.map(dept => {
        const count = Math.floor(Math.random() * 20) + 5 // 5-24人
        const isAbnormal = Math.random() > 0.8 // 20%概率异常
        const status = isAbnormal
          ? '<span style="color: #ff7875;">● 异常</span>'
          : '<span style="color: #52c41a;">● 正常</span>'
        return [dept, `${count}人`, status]
      })
    }
  }
}
</script>

<style lang="less">
#ranking-board {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 15px rgba(1, 153, 209, 0.2);
  display: flex;
  flex-direction: column;
  background: rgba(6, 30, 93, 0.5);
  border-radius: 15px;
  border: 1px solid rgba(1, 153, 209, .3);
  box-sizing: border-box;
  padding: 15px;
  color: #ffffff;
  overflow: hidden;

  .ranking-board-title {
    font-weight: bold;
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #ffffff;
    margin-bottom: 10px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  // 自定义滚动表格样式
  /deep/ .dv-scroll-board {
    .header {
      background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
      box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

      .header-item {
        color: #ffffff;
        font-weight: 600;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .rows {
      .row-item {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(25, 129, 246, 0.2) !important;
          transform: translateX(3px);
        }

        .ceil {
          color: #ffffff;
          font-size: 13px;

          // 序号列
          &:first-child {
            color: #ffb74d;
            font-weight: 600;
          }

          // 部门列
          &:nth-child(2) {
            color: #64b5f6;
            font-weight: 500;
          }

          // 人数列
          &:nth-child(3) {
            color: #81c784;
            font-weight: 500;
          }

          // 状态列
          &:nth-child(4) {
            font-weight: 600;
            color: #52c41a; // 默认正常状态颜色
          }
        }
      }
    }
  }
}
</style>
