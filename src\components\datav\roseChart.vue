<template>
  <div id="rose-chart">
    <div class="rose-chart-title">近一个月进出情况</div>
    <div ref="chart" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: '<PERSON><PERSON><PERSON>',
  data () {
    return {
      chart: null,
      option: {},
      timer: null
    }
  },
  methods: {
    initChart () {
      this.chart = echarts.init(this.$refs.chart)
      this.createData()

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    },
    createData () {
      // 生成近30天的日期数据
      const days = []
      const entryData = []
      const exitData = []

      for (let i = 29; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        days.push(`${date.getDate()}日`)

        // 模拟进出人数数据，实际项目中应该从API获取
        entryData.push(Math.floor(Math.random() * 500) + 1500) // 进入人数 1500-2000
        exitData.push(Math.floor(Math.random() * 500) + 1400) // 出去人数 1400-1900
      }

      this.option = {
        backgroundColor: 'transparent',
        grid: {
          top: '15%',
          left: '2',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        legend: {
          show: true,
          top: '5%',
          left: 'center',
          itemGap: 20,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          data: ['进门人次', '出门人次']
        },
        xAxis: {
          type: 'category',
          name: '日期',
          nameTextStyle: {
            color: '#fff',
            fontSize: 14
          },
          data: days,
          axisLabel: {
            color: '#fff',
            fontSize: 11
          },
          axisLine: {
            lineStyle: {
              color: '#03d3ec'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#03d3ec'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '人次',
          nameTextStyle: {
            color: '#fff',
            fontSize: 14
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#03d3ec'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            name: '进门人次',
            type: 'line',
            data: entryData,
            smooth: true,
            lineStyle: {
              color: '#1e88e5',
              width: 3
            },
            itemStyle: {
              color: '#1e88e5'
            },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '出门人次',
            type: 'line',
            data: exitData,
            smooth: true,
            lineStyle: {
              color: '#00bcd4',
              width: 3
            },
            itemStyle: {
              color: '#00bcd4'
            },
            symbol: 'circle',
            symbolSize: 6
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#03d3ec',
          textStyle: {
            color: '#fff'
          }
        }
      }

      if (this.chart) {
        this.chart.setOption(this.option)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()

      // 每30秒更新一次数据
      this.timer = setInterval(() => {
        this.createData()
      }, 30000)
    })
  },
  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', () => {
      this.chart && this.chart.resize()
    })
  }
}
</script>

<style lang="less">
#rose-chart {
  width: 100%;
  height: 100%;
  background-color: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(1, 153, 209, 0.3); // 边框
  border-radius: 15px;
  box-sizing: border-box;

  .rose-chart-title {
    height: 50px;
    font-weight: bold;
    text-indent: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  }

  .chart-container {
    height: calc(100% - 50px);
    width: 100%;
  }
}
</style>
