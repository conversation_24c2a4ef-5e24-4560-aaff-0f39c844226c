<template>
  <div id="vehicle-status-bar">
    <div class="status-container">
      <!-- 车辆进出情况标题 -->
      <div class="status-title">
        <i class="fas fa-car"></i>
        车辆进出情况
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicators">
        <!-- 进入车辆 -->
        <div class="status-item entry">
          <div class="status-dot"></div>
          <span class="status-label">进入车辆</span>
          <span class="status-count">
             <span>{{ entryCount }}</span>
          </span>
        </div>

        <!-- 离开车辆 -->
        <div class="status-item exit">
          <div class="status-dot"></div>
          <span class="status-label">离开车辆</span>
          <span class="status-count">
            <span>{{ exitCount }}</span>
          </span>
        </div>

        <!-- 当前在校 -->
        <div class="status-item current">
          <div class="status-dot"></div>
          <span class="status-label">当前在校</span>
          <span class="status-count current-count">
            <span>{{ currentCount }}</span>
          </span>
          <span class="unit">辆</span>
        </div>
      </div>

      <!-- 车位使用率 -->
      <div class="usage-rate">
        <span class="usage-label">车位使用率</span>
        <div class="usage-bar">
          <div class="usage-fill" :style="{ width: usageRate + '%' }"></div>
        </div>
        <span class="usage-percentage">{{ usageRate }}%</span>
        <span class="update-time">最后更新: {{ updateTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VehicleStatusBar',
  data () {
    return {
      entryCount: 45,
      exitCount: 38,
      currentCount: 127,
      usageRate: 85,
      updateTime: '11:55:37'
    }
  },
  computed: {
    entryVehicleConfig () {
      return {
        number: [this.entryCount],
        content: '{nt}',
        textAlign: 'center',
        style: {
          fill: '#4ade80',
          fontWeight: 'bold',
          fontSize: '28px'
        }
      }
    },
    exitVehicleConfig () {
      return {
        number: [this.exitCount],
        content: '{nt}',
        textAlign: 'center',
        style: {
          fill: '#f97316',
          fontWeight: 'bold',
          fontSize: '18px'
        }
      }
    },
    currentVehicleConfig () {
      return {
        number: [this.currentCount],
        content: '{nt}',
        textAlign: 'center',
        style: {
          fill: '#ef4444',
          fontWeight: 'bold',
          fontSize: '20px'
        }
      }
    }
  },
  methods: {
    updateData () {
      // 模拟数据更新
      this.entryCount = this.randomExtend(40, 50)
      this.exitCount = this.randomExtend(35, 45)
      this.currentCount = this.randomExtend(120, 135)
      this.usageRate = this.randomExtend(80, 90)

      // 更新时间
      const now = new Date()
      this.updateTime = now.toTimeString().slice(0, 8)
    },
    randomExtend (minNum, maxNum) {
      if (arguments.length === 1) {
        return parseInt(Math.random() * minNum + 1, 10)
      } else {
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
      }
    }
  },
  mounted () {
    // 每30秒更新一次数据
    setInterval(this.updateData, 30000)
  }
}
</script>

<style lang="less">
#vehicle-status-bar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .status-container {
    width: 100%;
    height: 60px;
    background: rgba(6, 30, 93, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(1, 153, 209, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    padding: 0 25px;
    gap: 30px;
    position: relative;
    overflow: hidden;

    // 背景光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    .status-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      white-space: nowrap;
      text-shadow: 0 0 10px rgba(25, 129, 246, 0.8);

      i {
        color: #03d3ec;
        font-size: 18px;
      }
    }

    .status-indicators {
      display: flex;
      align-items: center;
      gap: 25px;
      flex: 1;

      .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        .status-label {
          color: #b8c5d1;
          font-size: 14px;
          font-weight: 500;
        }

        .status-count {
          color: #ffffff;
          font-weight: bold;
          font-size: 16px;
          min-width: 30px;
          text-align: center;
        }

        .current-count {
          font-size: 18px;
        }

        .unit {
          color: #b8c5d1;
          font-size: 14px;
          margin-left: 2px;
        }

        &.entry .status-dot {
          background-color: #4ade80;
          box-shadow: 0 0 10px rgba(74, 222, 128, 0.6);
        }

        &.exit .status-dot {
          background-color: #f97316;
          box-shadow: 0 0 10px rgba(249, 115, 22, 0.6);
        }

        &.current .status-dot {
          background-color: #ef4444;
          box-shadow: 0 0 10px rgba(239, 68, 68, 0.6);
        }
      }
    }

    .usage-rate {
      display: flex;
      align-items: center;
      gap: 12px;
      white-space: nowrap;

      .usage-label {
        color: #b8c5d1;
        font-size: 14px;
        font-weight: 500;
      }

      .usage-bar {
        width: 120px;
        height: 8px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
        position: relative;

        .usage-fill {
          height: 100%;
          background: linear-gradient(90deg, #4ade80, #22c55e);
          border-radius: 4px;
          transition: width 0.8s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progress-shimmer 2s infinite;
          }
        }
      }

      .usage-percentage {
        color: #ffffff;
        font-weight: bold;
        font-size: 16px;
        min-width: 40px;
      }

      .update-time {
        color: #64748b;
        font-size: 12px;
        margin-left: 10px;
      }
    }
  }
}

// 动画效果
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes progress-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>
